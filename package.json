{"name": "youtube-window", "version": "1.0.0", "description": "A YouTube browser with ad-blocking capability", "main": "main.js", "scripts": {"start": "electron .", "dev": "nodemon --exec electron .", "build:arm64": "electron-builder build --mac --arm64", "build:x64": "electron-builder build --mac --x64", "build:universal": "electron-builder build --mac --universal"}, "author": "Your Name", "license": "MIT", "dependencies": {}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.youtube.window", "productName": "Simple Browser", "mac": {"category": "public.app-category.productivity", "target": {"target": "default", "arch": ["arm64", "x64"]}, "icon": "build/icon.icns", "extendInfo": {"CFBundleURLTypes": [{"CFBundleURLName": "HTTP protocol", "CFBundleURLSchemes": ["http", "https"]}], "CFBundleDocumentTypes": [{"CFBundleTypeName": "HTML document", "CFBundleTypeRole": "Viewer", "LSItemContentTypes": ["public.html", "public.xhtml"]}], "LSApplicationCategoryType": "public.app-category.productivity", "LSCanOpenProtocolsInComments": true, "LSEnvironment": {"LSUIElement": "0"}, "NSPrefPaneIconFile": "build/icon.icns", "NSPrefPaneIconLabel": "Simple Browser", "CFBundleTypeRole": "Browser"}, "extraResources": [{"from": "userscripts", "to": "userscripts"}, {"from": "homepage.html", "to": "homepage.html"}]}, "directories": {"output": "dist"}}}
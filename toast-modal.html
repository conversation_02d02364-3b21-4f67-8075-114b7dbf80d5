<!DOCTYPE html>
<html>
<head>
    <title>提示</title>
    <style>
        body {
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
        }
        .toast {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .close-button {
            cursor: pointer;
            background: none;
            border: none;
            color: #999;
            font-size: 16px;
            padding: 0 0 0 8px;
            margin-left: 8px;
            border-left: 1px solid #666;
        }
        .close-button:hover {
            color: white;
        }
    </style>
</head>
<body>
    <div class="toast">
        <span>已加入书签</span>
        <button class="close-button" onclick="window.close()">✕</button>
    </div>
    <script>
        // 3秒后自动关闭
        setTimeout(() => {
            window.close();
        }, 3000);
    </script>
</body>
</html> 
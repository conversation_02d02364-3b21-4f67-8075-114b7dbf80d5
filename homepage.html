<!DOCTYPE html>
<html>
<head>
  <title>Homepage</title>
  <style>
    body {
      margin: 0;
      padding: 40px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background: #f5f5f7;
      color: #1d1d1f;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: fixed;
      min-height: 100vh;
      position: relative;
    }

    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(245, 245, 247, 0.3);
      z-index: 0;
    }

    .container {
      position: relative;
      z-index: 1;
      max-width: 1200px;
      margin: 0 auto;
    }

    h1 {
      font-size: 28px;
      font-weight: 500;
      margin-bottom: 30px;
      color: #1d1d1f;
    }

    .cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 20px;
      padding: 20px 0;
    }

    .card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      color: inherit;
      position: relative;
    }

    .card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .card img {
      width: 48px;
      height: 48px;
      margin-bottom: 12px;
      border-radius: 10px;
    }

    .card h2 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .card p {
      margin: 8px 0 0;
      font-size: 14px;
      color: #666;
      text-align: center;
    }

    .delete-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 24px;
      height: 24px;
      border-radius: 12px;
      background: #f1f1f1;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #666;
      transition: all 0.2s ease;
      z-index: 1;
    }

    .delete-btn:hover {
      background: #ff3b30;
      color: white;
    }
  </style>
</head>
<body>
  <div class="container">
    
    <h1>书签</h1>
    <div class="cards" id="bookmarks-container">
      <!-- Bookmarks will be loaded here dynamically -->
    </div>
  </div>

  <script>
    // 存储已使用的颜色的色相值
    const usedHues = new Set();
    
    // 生成随机柔和的背景色
    function getRandomPastelColor() {
      const minHueDifference = 30; // 最小色相差异（度）
      let hue;
      let attempts = 0;
      const maxAttempts = 360;

      do {
        hue = Math.floor(Math.random() * 360);
        attempts++;

        // 检查是否与现有颜色有足够的差异
        const hasEnoughDifference = Array.from(usedHues).every(usedHue => {
          const diff = Math.min(
            Math.abs(hue - usedHue),
            360 - Math.abs(hue - usedHue)
          );
          return diff >= minHueDifference;
        });

        // 如果找到合适的色相或达到最大尝试次数，就退出循环
        if (hasEnoughDifference || attempts >= maxAttempts || usedHues.size === 0) {
          break;
        }

        // 如果尝试次数过多，说明当前的限制可能太严格，清空已使用的色相
        if (attempts >= maxAttempts) {
          usedHues.clear();
        }
      } while (true);

      usedHues.add(hue);
      return `hsl(${hue}, 75%, 85%)`; // 稍微增加饱和度，降低亮度使颜色更鲜明
    }

    // Load and display bookmarks
    function loadBookmarks() {
      // Request bookmarks from the main process
      window.postMessage({ type: 'getBookmarks' }, '*');
    }

    // Listen for bookmarks data
    window.addEventListener('message', (event) => {
      if (event.data.type === 'bookmarksResult') {
        const result = event.data.data;
        if (result.success) {
          const container = document.getElementById('bookmarks-container');
          container.innerHTML = ''; // Clear existing bookmarks
          
          result.bookmarks.forEach(bookmark => {
            const card = document.createElement('a');
            card.href = bookmark.url;
            card.className = 'card';
            card.style.backgroundColor = getRandomPastelColor();
            
            const title = document.createElement('h2');
            title.textContent = bookmark.title;
            
            const url = document.createElement('p');
            url.textContent = new URL(bookmark.url).hostname;

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-btn';
            deleteBtn.innerHTML = '×';
            deleteBtn.title = '删除书签';
            
            deleteBtn.addEventListener('click', async (e) => {
              e.preventDefault();
              e.stopPropagation();
              
              // Send delete request
              window.postMessage({ 
                type: 'deleteBookmark', 
                url: bookmark.url 
              }, '*');
            });
            
            card.appendChild(deleteBtn);
            card.appendChild(title);
            card.appendChild(url);
            
            card.addEventListener('click', (e) => {
              e.preventDefault();
              window.postMessage({ type: 'loadURL', url: bookmark.url }, '*');
            });
            
            container.appendChild(card);
          });
        }
      }
    });

    // Handle link clicks, notify main process to load URL
    document.querySelectorAll('.card').forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        const url = card.getAttribute('href');
        window.postMessage({ type: 'loadURL', url: url }, '*');
      });
    });

    // Load bookmarks when the page loads
    loadBookmarks();

    // Drag and drop functionality
    document.body.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.stopPropagation();
    });

    document.body.addEventListener('drop', (e) => {
      e.preventDefault();
      e.stopPropagation();
      
      const file = e.dataTransfer.files[0];
      if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (event) => {
          document.body.style.backgroundImage = `url(${event.target.result})`;
          // Save the background image to localStorage
          localStorage.setItem('backgroundImage', event.target.result);
        };
        reader.readAsDataURL(file);
      }
    });

    // Load saved background image on page load
    window.addEventListener('load', () => {
      const savedBackground = localStorage.getItem('backgroundImage');
      if (savedBackground) {
        document.body.style.backgroundImage = `url(${savedBackground})`;
      }
    });
  </script>
</body>
</html> 
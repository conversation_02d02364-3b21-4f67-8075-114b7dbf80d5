<!DOCTYPE html>
<html>
<head>
  <title>Simple Browser</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: white;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      -webkit-app-region: no-drag;
    }
    
    .titlebar {
      height: 30px;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      -webkit-app-region: no-drag;
      z-index: 9999;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      display: none;
      opacity: 0;
      transition: opacity 0.2s ease-in-out, background-color 0.3s ease-in-out;
      pointer-events: none;
    }

    #progress-bar {
      position: fixed;
      left: 0;
      width: 0;
      height: 2px;
      background: #2196F3;
      z-index: 10000;
      transition: width 0.2s ease-out, top 0.2s ease-in-out;
      top: 0;
    }

    #progress-bar.shifted {
      top: 30px;
    }

    .content {
      flex: 1;
      display: flex;
      margin-top: 0;
      padding: 0;
      transition: margin-top 0.2s ease-in-out;
      -webkit-app-region: no-drag;
    }

    .content.shifted {
      margin-top: 30px;
    }

    .titlebar.visible {
      display: block;
      opacity: 1;
      -webkit-app-region: drag;
      pointer-events: auto;
    }

    #webview {
      flex: 1;
      border: none;
      margin: 0;
      padding: 0;
      -webkit-app-region: no-drag;
    }

    #url-modal, #opacity-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 10000;
    }

    #drag-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99999;
      -webkit-app-region: drag;
      cursor: move;
      background: rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .modal-content {
      position: fixed;
      top:15%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10001;
    }

    .modal-input {
      width: 300px;
      padding: 8px 12px;
      font-size: 14px;
      border: 1px solid #ddd;
      border-radius: 6px;
      outline: none;
    }

    .modal-input:focus {
      border-color: #007AFF;
      box-shadow: 0 0 0 2px rgba(0,122,255,0.2);
    }

    #url-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      display: none;
      max-height: 400px;
      overflow-y: auto;
      overflow-x: hidden;
      margin-top: 8px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.3), 0 2px 8px 0 rgba(0, 0, 0, 0.1);
      z-index: 10000;
      padding: 4px;
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 122, 255, 0.3) transparent;
    }

    .url-suggestion {
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 8px;
      margin: 2px 4px;
      font-size: 13px;
      font-weight: 500;
      border-left: 3px solid transparent;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background: rgba(255, 255, 255, 0.1);
    }

    .url-suggestion:hover {
      background: rgba(0, 122, 255, 0.1);
      transform: translateX(2px);
      border-left-color: rgba(0, 122, 255, 0.5);
    }

    .url-suggestion.selected {
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.8), rgba(0, 122, 255, 0.6));
      color: white;
      transform: translateX(4px);
      border-left-color: rgba(255, 255, 255, 0.8);
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    }

    /* 自定义滚动条样式 */
    #url-suggestions::-webkit-scrollbar {
      width: 6px;
    }
    #url-suggestions::-webkit-scrollbar-track {
      background: transparent;
    }
    #url-suggestions::-webkit-scrollbar-thumb {
      background: rgba(0, 122, 255, 0.3);
      border-radius: 3px;
    }
    #url-suggestions::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 122, 255, 0.5);
    }

    /* Search modal styles */
    #search-modal {
      display: none;
      position: fixed;
      top: 0;
      right: 20px;
      background: white;
      padding: 8px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      margin-top: 10px;
    }

    .search-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    #search-input {
      width: 200px;
      padding: 6px 10px;
      font-size: 14px;
      border: 1px solid #ddd;
      border-radius: 4px;
      outline: none;
    }

    #search-input:focus {
      border-color: #007AFF;
      box-shadow: 0 0 0 2px rgba(0,122,255,0.2);
    }

    .search-count {
      font-size: 12px;
      color: #666;
      margin: 0 8px;
    }

    .search-button {
      padding: 4px 8px;
      background: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }

    .search-button:hover {
      background: #e5e5e5;
    }

    .modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
    }

    .suggest-list {
        position: absolute;
        width: 100%;
        max-height: 400px;
        overflow-y: auto;
        background: white;
        z-index: 999;
    }
  </style>
</head>
<body>
  <div id="progress-bar"></div>
  <div class="titlebar"></div>
  
  <div class="content">
    <webview id="webview" 
             src="about:blank" 
             allowpopups="true"
             partition="persist:main"
             preload="./preload.js"
             webpreferences="contextIsolation=no, nodeIntegration=no, nativeWindowOpen=true"
             plugins></webview>
  </div>

  <div id="drag-overlay"></div>

  <div id="url-modal">
    <div class="modal-content">
      <input type="text" id="url-input" class="modal-input" placeholder="输入网址">
      <div id="url-suggestions"></div>
    </div>
  </div>

  <div id="opacity-modal">
    <div class="modal-content">
      <input type="text" id="opacity-input" class="modal-input" placeholder="输入透明度 (0.1-1.0)">
    </div>
  </div>

  <div id="search-modal">
    <div class="search-container">
      <input type="text" id="search-input" placeholder="搜索页面内容">
      <span class="search-count" id="search-count"></span>
      <button class="search-button" id="search-prev">上一个</button>
      <button class="search-button" id="search-next">下一个</button>
      <button class="search-button" id="search-close">关闭</button>
    </div>
  </div>

  <script src="renderer.js"></script>
</body>
</html> 
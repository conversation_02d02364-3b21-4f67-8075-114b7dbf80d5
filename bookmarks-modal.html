<!DOCTYPE html>
<html>
<head>
    <title>书签</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
            background: #f5f5f7;
            color: #333;
        }
        .modal-container {
            padding: 20px;
            height: 100vh;
            width: 800px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        .modal-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
            color: #1a1a1a;
        }
        .close-button {
            cursor: pointer;
            background: none;
            border: none;
            font-size: 20px;
            color: #666;
            padding: 8px;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        .close-button:hover {
            background: #e0e0e0;
            color: #333;
        }
        #bookmarks-list {
            overflow-y: auto;
            flex-grow: 1;
        }
        .bookmark-item {
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        .bookmark-item:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }
        .bookmark-content {
            flex-grow: 1;
            margin-right: 16px;
            overflow: hidden;
            cursor: pointer;
        }
        .bookmark-title {
            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .bookmark-url {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .bookmark-actions {
            display: flex;
            gap: 8px;
        }
        .delete-button {
            cursor: pointer;
            background: #ff3b30;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        .delete-button:hover {
            background: #ff1f1f;
        }
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        .empty-state p {
            margin: 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="modal-container" id="modal-container">
        <div class="modal-header">
            <h2>书签</h2>
            <button class="close-button" onclick="window.close()">✕</button>
        </div>
        <div id="bookmarks-list"></div>
    </div>
    <script>
        const { ipcRenderer } = require('electron');

        // Add click outside to close
        document.getElementById('modal-container').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        document.body.addEventListener('click', () => {
            window.close();
        });

        async function loadBookmarks() {
            try {
                const result = await ipcRenderer.invoke('get-bookmarks');
                if (!result.success) {
                    throw new Error(result.message);
                }

                const bookmarks = result.bookmarks;
                const bookmarksList = document.getElementById('bookmarks-list');
                bookmarksList.innerHTML = '';
                
                if (bookmarks.length === 0) {
                    bookmarksList.innerHTML = `
                        <div class="empty-state">
                            <p>还没有添加任何书签</p>
                            <p>按 Cmd+B 将当前页面添加到书签</p>
                        </div>
                    `;
                    return;
                }
                
                bookmarks.forEach((bookmark) => {
                    const bookmarkItem = document.createElement('div');
                    bookmarkItem.className = 'bookmark-item';
                    
                    const content = document.createElement('div');
                    content.className = 'bookmark-content';
                    content.onclick = () => {
                        ipcRenderer.send('load-url', bookmark.url);
                    };
                    
                    const title = document.createElement('div');
                    title.className = 'bookmark-title';
                    title.textContent = bookmark.title || '未命名';
                    
                    const url = document.createElement('div');
                    url.className = 'bookmark-url';
                    url.textContent = bookmark.url;
                    
                    content.appendChild(title);
                    content.appendChild(url);
                    
                    const actions = document.createElement('div');
                    actions.className = 'bookmark-actions';
                    
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'delete-button';
                    deleteBtn.textContent = '删除';
                    deleteBtn.onclick = async (e) => {
                        e.stopPropagation();
                        try {
                            const result = await ipcRenderer.invoke('delete-bookmark', bookmark.url);
                            if (result.success) {
                                loadBookmarks(); // 重新加载书签列表
                            }
                        } catch (error) {
                            console.error('Error deleting bookmark:', error);
                        }
                    };
                    
                    actions.appendChild(deleteBtn);
                    bookmarkItem.appendChild(content);
                    bookmarkItem.appendChild(actions);
                    bookmarksList.appendChild(bookmarkItem);
                });
            } catch (error) {
                console.error('Error loading bookmarks:', error);
                const bookmarksList = document.getElementById('bookmarks-list');
                bookmarksList.innerHTML = `
                    <div class="empty-state">
                        <p>加载书签时出错</p>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        loadBookmarks();
    </script>
</body>
</html> 
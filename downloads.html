<!DOCTYPE html>
<html>
<head>
    <title>下载管理器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .download-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .download-info {
            flex-grow: 1;
        }
        .filename {
            font-weight: 500;
            margin-bottom: 5px;
        }
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #eee;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background: #2196F3;
            width: 0%;
            transition: width 0.5s ease-out;
        }
        .status {
            color: #666;
            font-size: 0.9em;
        }
        .controls button {
            padding: 6px 12px;
            margin-left: 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #f0f0f0;
            transition: background-color 0.2s;
        }
        .controls button:hover:not(:disabled) {
            background: #e0e0e0;
        }
        .controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .empty-state {
            text-align: center;
            color: #666;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <div id="downloads-list">
        <div class="empty-state">暂无下载任务</div>
    </div>

    <script>
        const { ipcRenderer, shell } = require('electron');

        // 监听新的下载任务
        ipcRenderer.on('download-started', (event, item) => {
            addDownloadItem(item);
        });

        // 监听下载进度更新
        ipcRenderer.on('download-progress', (event, data) => {
            updateDownloadProgress(data);
        });

        // 监听下载状态变化
        ipcRenderer.on('download-state', (event, data) => {
            updateDownloadState(data);
        });

        function addDownloadItem(item) {
            const downloadsList = document.getElementById('downloads-list');
            const emptyState = downloadsList.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            // 检查是否已存在相同ID的下载项
            const existingItem = document.getElementById(`download-${item.id}`);
            if (existingItem) {
                console.log('下载项已存在，跳过添加:', item.filename);
                return;
            }

            const downloadItem = document.createElement('div');
            downloadItem.className = 'download-item';
            downloadItem.id = `download-${item.id}`;
            
            // 计算初始进度（如果有）
            const initialProgress = item.progress || 0;
            const initialWidth = `${initialProgress}%`;
            
            downloadItem.innerHTML = `
                <div class="download-info">
                    <div class="filename">${item.filename}</div>
                    <div class="progress-bar">
                        <div class="progress" style="width: ${initialWidth}" data-percent="${initialProgress}"></div>
                    </div>
                    <div class="status" data-transferred="0" data-total="${item.totalBytes || 0}">准备下载...</div>
                </div>
                <div class="controls">
                    <button class="pause-btn" onclick="pauseDownload('${item.id}')">暂停</button>
                    <button class="resume-btn" onclick="resumeDownload('${item.id}')" disabled>继续</button>
                    <button class="cancel-btn" onclick="cancelDownload('${item.id}')">取消</button>
                    <button class="open-dir-btn" onclick="openDownloadDirectory('${item.id}')">打开目录</button>
                </div>
            `;
            downloadsList.appendChild(downloadItem);

            // 设置初始按钮状态
            const pauseBtn = downloadItem.querySelector('.pause-btn');
            const resumeBtn = downloadItem.querySelector('.resume-btn');
            const cancelBtn = downloadItem.querySelector('.cancel-btn');
            const openDirBtn = downloadItem.querySelector('.open-dir-btn');
            const status = downloadItem.querySelector('.status');

            console.log('创建下载项，状态:', item.state);

            // 根据下载状态设置按钮状态
            if (item.state === 'paused') {
                console.log('设置暂停状态:', item.id);
                pauseBtn.disabled = true;
                resumeBtn.disabled = false;
                status.textContent = '下载已暂停';
            } else if (item.state === 'interrupted') {
                pauseBtn.disabled = true;
                resumeBtn.disabled = true;
                cancelBtn.disabled = true;
                openDirBtn.disabled = true;
                status.textContent = '下载已中断';
            }
        }

        function updateDownloadProgress(data) {
            const item = document.getElementById(`download-${data.id}`);
            if (item) {
                const progress = item.querySelector('.progress');
                const status = item.querySelector('.status');
                const filename = item.querySelector('.filename');
                const pauseBtn = item.querySelector('.pause-btn');
                const resumeBtn = item.querySelector('.resume-btn');
                const cancelBtn = item.querySelector('.cancel-btn');
                const openDirBtn = item.querySelector('.open-dir-btn');
                
                // 使用 requestAnimationFrame 来平滑更新进度条
                requestAnimationFrame(() => {
                    // 如果有新的文件名，更新文件名显示
                    if (data.filename) {
                        filename.textContent = data.filename;
                    }
                    
                    progress.style.width = `${data.percent}%`;
                    // 保存当前进度到DOM元素中，以便在重新打开窗口时恢复
                    progress.dataset.percent = data.percent;
                    status.textContent = `${formatBytes(data.transferred)} / ${formatBytes(data.total)} (${data.percent}%)`;
                    status.dataset.transferred = data.transferred;
                    status.dataset.total = data.total;

                    // 保存当前状态到DOM元素
                    progress.dataset.state = data.state;

                    // 根据状态更新按钮
                    if (data.percent >= 100) {
                        // 下载完成状态
                        pauseBtn.disabled = true;
                        resumeBtn.disabled = true;
                        cancelBtn.disabled = true;
                        openDirBtn.disabled = false;
                        status.textContent = '下载完成';
                    } else if (data.state === 'paused') {
                        // 暂停状态
                        pauseBtn.disabled = true;
                        resumeBtn.disabled = false;
                        cancelBtn.disabled = false;
                        openDirBtn.disabled = false;
                        status.textContent = '下载已暂停';
                    } else if (data.state === 'progressing') {
                        // 正在下载状态
                        pauseBtn.disabled = false;
                        resumeBtn.disabled = true;
                        cancelBtn.disabled = false;
                        openDirBtn.disabled = false;
                    }
                });
            }
        }

        function updateDownloadState(data) {
            console.log('更新下载状态:', data);
            const item = document.getElementById(`download-${data.id}`);
            if (item) {
                const status = item.querySelector('.status');
                const pauseBtn = item.querySelector('.pause-btn');
                const resumeBtn = item.querySelector('.resume-btn');
                const cancelBtn = item.querySelector('.cancel-btn');
                const openDirBtn = item.querySelector('.open-dir-btn');
                const filename = item.querySelector('.filename');
                const progress = item.querySelector('.progress');

                // 如果有新的文件名，更新文件名显示
                if (data.filename) {
                    filename.textContent = data.filename;
                }

                // 保存状态到进度条元素
                progress.dataset.state = data.state;
                status.textContent = data.state;

                switch (data.state) {
                    case '下载完成':
                        pauseBtn.disabled = true;
                        resumeBtn.disabled = true;
                        cancelBtn.disabled = true;
                        openDirBtn.disabled = false;
                        break;
                    case '下载已暂停':
                        console.log('设置暂停状态按钮:', data.id);
                        pauseBtn.disabled = true;
                        resumeBtn.disabled = false;
                        cancelBtn.disabled = false;
                        openDirBtn.disabled = false;
                        break;
                    case '下载已取消':
                    case '已取消':
                    case '取消下载失败':
                    case '下载已中断':
                    case '下载出错':
                        pauseBtn.disabled = true;
                        resumeBtn.disabled = true;
                        cancelBtn.disabled = true;
                        openDirBtn.disabled = true;
                        // 添加一个延迟后移除下载项
                        setTimeout(() => {
                            item.remove();
                            // 检查是否还有其他下载项
                            const downloadsList = document.getElementById('downloads-list');
                            if (!downloadsList.querySelector('.download-item')) {
                                downloadsList.innerHTML = '<div class="empty-state">暂无下载任务</div>';
                            }
                        }, 3000); // 3秒后移除
                        break;
                    default:
                        // 正在下载时的状态
                        pauseBtn.disabled = false;
                        resumeBtn.disabled = true;
                        cancelBtn.disabled = false;
                        openDirBtn.disabled = false;
                }
            }
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function pauseDownload(id) {
            ipcRenderer.send('pause-download', id);
        }

        function resumeDownload(id) {
            ipcRenderer.send('resume-download', id);
            const item = document.getElementById(`download-${id}`);
            if (item) {
                const pauseBtn = item.querySelector('.pause-btn');
                const resumeBtn = item.querySelector('.resume-btn');
                pauseBtn.disabled = false;
                resumeBtn.disabled = true;
            }
        }

        function cancelDownload(id) {
            ipcRenderer.send('cancel-download', id);
        }

        function openDownloadDirectory(id) {
            ipcRenderer.send('open-download-directory', id);
        }
    </script>
</body>
</html> 
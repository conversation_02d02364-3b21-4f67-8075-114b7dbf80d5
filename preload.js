const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Listen for messages from homepage
window.addEventListener('message', (event) => {
  if (event.data.type === 'loadURL') {
    ipcRenderer.sendToHost('loadURL', event.data.url);
  } else if (event.data.type === 'getBookmarks') {
    // Forward the get-bookmarks request to the main process
    ipcRenderer.invoke('get-bookmarks').then(result => {
      // Send the result back to the webpage
      window.postMessage({ type: 'bookmarksResult', data: result }, '*');
    });
  } else if (event.data.type === 'deleteBookmark') {
    // Forward the delete-bookmark request to the main process
    ipcRenderer.invoke('delete-bookmark', event.data.url).then(result => {
      // If deletion was successful, reload bookmarks
      if (result.success) {
        // Trigger a reload of bookmarks
        window.postMessage({ type: 'getBookmarks' }, '*');
      }
    });
  } else if (event.data.type === 'create-new-window') {
    // Forward the create-new-window request to the host
    ipcRenderer.sendToHost('create-new-window', event.data.url);
  } else if (event.data.type === 'stopCurrentNavigation') {
    // Forward the stop-current-navigation request to the host
    ipcRenderer.sendToHost('stop-current-navigation', event.data.url);
  }
});

// Expose ipcRenderer.invoke for bookmarks operations
window.ipcRenderer = {
  invoke: async (channel, ...args) => {
    if (channel === 'get-bookmarks') {
      return ipcRenderer.invoke('get-bookmarks');
    }
    return null;
  }
};

// Add throttling related variables
let lastMouseNavigationTime = 0;
const MOUSE_NAVIGATION_COOLDOWN = 500; // 500ms cooldown time

document.addEventListener('mouseup', (e) => {
  console.log('Mouse button clicked:', e.button);
  const now = Date.now();
  
  // Check if in cooldown period
  if (now - lastMouseNavigationTime < MOUSE_NAVIGATION_COOLDOWN) {
    return;
  }
  
  if (e.button === 3 || e.button === 8) {
    lastMouseNavigationTime = now;
    ipcRenderer.sendToHost('go-back');
  }
  else if (e.button === 4 || e.button === 9) {
    lastMouseNavigationTime = now;
    ipcRenderer.sendToHost('go-forward');
  }
}, false);

// Swipe navigation related variables
let lastNavigationTime = 0;
let lastWheelTime = 0;
let swipeTimeout = null;
let totalDeltaX = 0;

const NAVIGATION_COOLDOWN = 1000; // Navigation cooldown time (ms)
const SWIPE_THRESHOLD = 50;      // Swipe threshold
const SWIPE_TIMEOUT = 100;       // Swipe end detection time (ms)

// Trigger navigation
function triggerNavigation() {
  const now = Date.now();
  
  // Check if in cooldown period
  if (now - lastNavigationTime < NAVIGATION_COOLDOWN) {
    return;
  }

  // Check if threshold is reached
  if (Math.abs(totalDeltaX) >= SWIPE_THRESHOLD) {
    lastNavigationTime = now;
    const direction = Math.sign(totalDeltaX);

    if (direction > 0) {
      console.log('Going forward...');
      ipcRenderer.sendToHost('go-forward');
    } else {
      console.log('Going back...');
      ipcRenderer.sendToHost('go-back');
    }
  }

  // Reset state
  totalDeltaX = 0;
}

// Handle swipe events
function handleWheel(e) {
  const now = Date.now();

  // Only handle horizontal swipes (two-finger left/right swipe)
  if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
    e.preventDefault();

    // Clear previous timeout
    if (swipeTimeout) {
      clearTimeout(swipeTimeout);
    }

    // If it's a new swipe sequence
    if (now - lastWheelTime > NAVIGATION_COOLDOWN) {
      totalDeltaX = 0;
    }

    // Accumulate swipe distance
    totalDeltaX += e.deltaX;
    lastWheelTime = now;

    // Set new timeout to trigger navigation when swipe ends
    swipeTimeout = setTimeout(() => {
      triggerNavigation();
      swipeTimeout = null;
    }, SWIPE_TIMEOUT);
  }
}

// Add zoom related variables
let lastScale = 1.0;
const MIN_SCALE = 0.5;
const MAX_SCALE = 3.0;

// Handle all wheel events
document.addEventListener('wheel', (e) => {
    // If Command/Ctrl key is pressed
    if (e.metaKey || e.ctrlKey) {
        e.preventDefault();
        e.stopPropagation();
        
        // Calculate zoom increment
        const delta = -e.deltaY * 0.01; // Convert to more suitable zoom increment
        ipcRenderer.sendToHost('zoom-wheel', delta);
        
        console.log('Wheel zoom:', delta); // Debug log
    }
    // Otherwise continue processing existing horizontal swipe logic
    else if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        handleWheel(e);
    }
}, { passive: false });

// Handle pinch/spread gestures
let initialGestureDistance = null;

document.addEventListener('touchstart', (e) => {
    if (e.touches.length === 2) {
        e.preventDefault();
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        initialGestureDistance = Math.hypot(
            touch2.clientX - touch1.clientX,
            touch2.clientY - touch1.clientY
        );
        console.log('Touch start:', initialGestureDistance); // Debug log
    }
}, { passive: false });

document.addEventListener('touchmove', (e) => {
    if (e.touches.length === 2 && initialGestureDistance !== null) {
        e.preventDefault();
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        const currentDistance = Math.hypot(
            touch2.clientX - touch1.clientX,
            touch2.clientY - touch1.clientY
        );
        
        const scale = currentDistance / initialGestureDistance;
        ipcRenderer.sendToHost('zoom-gesture', scale);
        
        console.log('Touch move:', scale); // Debug log
    }
}, { passive: false });

document.addEventListener('touchend', (e) => {
    if (e.touches.length < 2) {
        initialGestureDistance = null;
        console.log('Touch end'); // Debug log
    }
}, { passive: false });

// Modify userScripts API - directly expose to window
window.userScripts = {
  loadUserScripts: () => ipcRenderer.invoke('load-user-scripts')
};

// Add GM_ functions that might be needed by userscripts
window.GM_info = {
  script: {
    name: 'userscript',
    namespace: 'electron-userscript'
  },
  version: '1.0'
};

window.GM_addStyle = (css) => {
  const style = document.createElement('style');
  style.textContent = css;
  document.head.appendChild(style);
  return style;
};

window.GM_xmlhttpRequest = function(details) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    xhr.open(details.method || 'GET', details.url, true);
    
    // Set timeout if specified
    if (details.timeout) {
      xhr.timeout = details.timeout;
    }
    
    // Set responseType if specified
    if (details.responseType) {
      xhr.responseType = details.responseType;
    }
    
    // Set all the requested headers
    if (details.headers) {
      Object.entries(details.headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });
    }

    // Set overrideMimeType if specified
    if (details.overrideMimeType) {
      xhr.overrideMimeType(details.overrideMimeType);
    }

    // Handle successful response
    xhr.onload = function() {
      const response = {
        status: xhr.status,
        statusText: xhr.statusText,
        responseHeaders: xhr.getAllResponseHeaders(),
        readyState: xhr.readyState,
        finalUrl: xhr.responseURL,
        response: xhr.response,
        responseText: xhr.responseText
      };
      
      if (details.onload) {
        details.onload(response);
      }
      resolve(response);
    };

    // Handle network errors
    xhr.onerror = function(error) {
      const response = {
        error: error,
        status: xhr.status,
        statusText: xhr.statusText,
        readyState: xhr.readyState
      };
      
      if (details.onerror) {
        details.onerror(response);
      }
      reject(response);
    };

    // Handle timeouts
    xhr.ontimeout = function() {
      const response = {
        error: 'timeout',
        status: xhr.status,
        statusText: xhr.statusText,
        readyState: xhr.readyState
      };
      
      if (details.ontimeout) {
        details.ontimeout(response);
      }
      reject(response);
    };

    // Handle progress if requested
    if (details.onprogress) {
      xhr.onprogress = details.onprogress;
    }

    // Handle upload progress if requested
    if (details.upload && details.upload.onprogress) {
      xhr.upload.onprogress = details.upload.onprogress;
    }

    // Handle abort if requested
    if (details.onabort) {
      xhr.onabort = details.onabort;
    }

    // Send the request with optional data
    try {
      xhr.send(details.data);
    } catch (error) {
      reject({
        error: error,
        status: 0,
        statusText: 'Request failed to send'
      });
    }

    // Return the XHR object for potential abort() calls
    return xhr;
  });
};

window.GM_setValue = (key, value) => {
  localStorage.setItem(`GM_${key}`, JSON.stringify(value));
};

window.GM_getValue = (key, defaultValue) => {
  const value = localStorage.getItem(`GM_${key}`);
  return value === null ? defaultValue : JSON.parse(value);
};

// Add missing GM_ functions
window.GM_deleteValue = (key) => {
  localStorage.removeItem(`GM_${key}`);
};

window.GM_listValues = () => {
  const values = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key.startsWith('GM_')) {
      values.push(key.slice(3)); // Remove 'GM_' prefix
    }
  }
  return values;
};

window.GM_getResourceText = (name) => {
  return '';  // Implement if needed
};

window.GM_getResourceURL = (name) => {
  return '';  // Implement if needed
};

window.GM_registerMenuCommand = (name, fn) => {
  // Could implement this with a custom UI if needed
  console.log('Menu command registered:', name);
};

window.GM_unregisterMenuCommand = (name) => {
  // Could implement this with a custom UI if needed
  console.log('Menu command unregistered:', name);
};

window.GM_openInTab = (url, options) => {
  window.open(url, '_blank');
};

window.GM_download = (details) => {
  const a = document.createElement('a');
  a.href = details.url;
  a.download = details.name || '';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

window.GM_log = console.log.bind(console);

window.GM_notification = (details) => {
  if (Notification.permission === 'granted') {
    new Notification(details.title || 'Notification', {
      body: details.text,
      icon: details.image
    });
  }
};

// Add unsafeWindow
window.unsafeWindow = window;

// 添加事件处理函数来防止重复点击
let lastClickTime = 0;
let lastClickedUrl = null;
const CLICK_DEBOUNCE_TIME = 300; // 300ms防抖时间

document.addEventListener('click', (e) => {
  // Handle Shift + Click for any element to force new window
  if (e.shiftKey || isShiftKeyPressed) {
    // Try to find a URL from the clicked element or its parents
    let targetElement = e.target;
    let targetUrl = null;

    // Check up to 5 parent levels to find a URL
    for (let i = 0; i < 5 && targetElement && !targetUrl; i++) {
      targetUrl = targetElement.href ||
                 targetElement.getAttribute('data-href') ||
                 targetElement.getAttribute('formaction') ||
                 targetElement.getAttribute('data-url') ||
                 targetElement.getAttribute('data-link');

      if (!targetUrl) {
        targetElement = targetElement.parentElement;
      }
    }

    // If we found a URL and it's http/https, create a new window
    if (targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))) {
      e.preventDefault();
      e.stopPropagation();
      console.log('Shift+Click: Opening in new window:', targetUrl);
      ipcRenderer.sendToHost('create-new-window', targetUrl);
      return;
    }
  }

  // Continue with original link click prevention logic
  if (e.target.tagName === 'A') {
    const currentTime = Date.now();
    const targetUrl = e.target.href;

    // 检查是否是重复点击同一个链接
    if (targetUrl &&
        currentTime - lastClickTime < CLICK_DEBOUNCE_TIME &&
        lastClickedUrl === targetUrl) {
      console.log('Debouncing duplicate click on:', targetUrl);
      e.preventDefault();
      e.stopPropagation();
      return;
    }

    // 更新最后点击信息
    lastClickTime = currentTime;
    lastClickedUrl = targetUrl;
  }
});

// Add early link and button click handler that works before page fully loads
function installEarlyClickHandler() {
  // Create a MutationObserver to watch for DOM changes
  const observer = new MutationObserver((mutations) => {
    // Process all added nodes to apply click handlers
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        // Check if the node is an element
        if (node.nodeType === 1) {
          // Add handler to all elements to check for shift+click
          if (node.nodeType === 1) {
            attachClickHandler(node);
          }
          
          // Also check all child nodes that are elements
          if (node.querySelectorAll) {
            const elements = node.querySelectorAll('*');
            elements.forEach(el => attachClickHandler(el));
          }
        }
      });
    });
  });
  
  // Function to attach click handler to an element
  function attachClickHandler(element) {
    if (element.__handlerAttached) return; // Prevent double handlers
    
    element.addEventListener('click', (e) => {
      // First check for Shift key - this overrides all other behavior
      if (e.shiftKey || isShiftKeyPressed) {
        // Try to find a URL from the clicked element or its parents
        let targetElement = e.target;
        let targetUrl = null;
        
        // Check up to 5 parent levels to find a URL
        for (let i = 0; i < 5 && targetElement && !targetUrl; i++) {
          targetUrl = targetElement.href || 
                     targetElement.getAttribute('data-href') || 
                     targetElement.getAttribute('formaction') || 
                     targetElement.getAttribute('data-url') || 
                     targetElement.getAttribute('data-link');
          
          if (!targetUrl) {
            targetElement = targetElement.parentElement;
          }
        }

        // If we found a URL and it's http/https, create a new window
        if (targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))) {
          e.preventDefault();
          e.stopPropagation();
          console.log('Shift+Click (early handler): Opening in new window:', targetUrl);
          ipcRenderer.sendToHost('create-new-window', targetUrl);
          return;
        }
      }
      
      // If not shift+click, continue with original logic for links and buttons
      
      // Get target URL
      let targetUrl = element.href || element.getAttribute('data-href');
      
      // For buttons, check additional attributes
      if (element.tagName === 'BUTTON') {
        targetUrl = targetUrl || 
                   element.getAttribute('formaction') || 
                   element.getAttribute('data-url') ||
                   element.getAttribute('data-link');
      }
      
      // Check for target="_blank"
      let hasTargetBlank = element.getAttribute('target') === '_blank' || 
                           element.getAttribute('rel')?.includes('external');
      
      // Skip handling if:
      // 1. Element has onclick attribute
      // 2. URL is javascript: protocol
      // 3. Has target="_blank" but URL is not http/https
      if (element.hasAttribute('onclick') || 
          targetUrl?.startsWith('javascript:') ||
          (hasTargetBlank && !(targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))))) {
        return;
      }
      
      // Handle HTTP/HTTPS URLs
      if (targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))) {
        // If link should open in new window
        if (hasTargetBlank) {
          e.preventDefault();
          // Send message to create new window
          ipcRenderer.sendToHost('create-new-window', targetUrl);
          return;
        }

        // For normal navigation, check for duplicate clicks first
        const currentTime = Date.now();
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_TIME &&
            lastClickedUrl === targetUrl) {
          console.log('Preventing duplicate navigation to:', targetUrl);
          e.preventDefault();
          e.stopPropagation();
          return;
        }

        // Update click tracking
        lastClickTime = currentTime;
        lastClickedUrl = targetUrl;

        // Stop current loading and notify target URL
        console.log('Link clicked: Stopping current navigation before loading:', targetUrl);
        ipcRenderer.sendToHost('stop-current-navigation', targetUrl);

        // Otherwise let the default behavior happen
      }
    }, true);
    
    // Mark as handled
    element.__handlerAttached = true;
  }
  
  // Start observing as soon as body is available
  function startObserving() {
    if (document.body) {
      // First handle existing elements - attach to ALL elements
      const elements = document.querySelectorAll('*');
      elements.forEach(el => attachClickHandler(el));
      
      // Then observe future changes
      observer.observe(document.body, { 
        childList: true, 
        subtree: true 
      });
      
      // Also override window.open
      overrideWindowOpen();
    } else {
      // If body isn't available yet, try again soon
      setTimeout(startObserving, 10);
    }
  }
  
  // Override window.open
  function overrideWindowOpen() {
    const originalWindowOpen = window.open;
    window.open = function(url, target, features) {
      // Check for Shift key pressed during window.open call
      if ((window.event && window.event.shiftKey || isShiftKeyPressed) && url && (url.startsWith('http://') || url.startsWith('https://'))) {
        ipcRenderer.sendToHost('create-new-window', url);
        return null;
      }
      
      // Let original window.open handle these cases:
      // 1. Has window features
      // 2. Not an http/https URL
      if ((features && features.trim() !== '') || !url || !(url.startsWith('http://') || url.startsWith('https://'))) {
        return originalWindowOpen(url, target, features);
      }
      
      // Handle target="_blank" case
      if (target === '_blank') {
        ipcRenderer.sendToHost('create-new-window', url);
        return null;
      }
      
      // Let default behavior handle the rest
      return originalWindowOpen(url, target, features);
    };
  }
  
  // Start the process
  startObserving();
}

// Track Shift key state globally to handle programmatic navigation while Shift is pressed
let isShiftKeyPressed = false;

document.addEventListener('keydown', (e) => {
  if (e.key === 'Shift') {
    isShiftKeyPressed = true;
  }
});

document.addEventListener('keyup', (e) => {
  if (e.key === 'Shift') {
    isShiftKeyPressed = false;
  }
});

// Make sure we reset the shift key state when window loses focus
window.addEventListener('blur', () => {
  isShiftKeyPressed = false;
});

// Call immediately when preload script runs
installEarlyClickHandler();

// Also make sure to run again when DOM content is loaded
document.addEventListener('DOMContentLoaded', installEarlyClickHandler); 

// 密码管理功能
class PasswordManager {
  constructor() {
    this.isDetectingPassword = false;
    this.pendingPasswordData = null;
    this.tempPasswordData = null;
    this.init();
  }

  init() {
    // 等待DOM加载完成后开始检测
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.startPasswordDetection());
    } else {
      this.startPasswordDetection();
    }
  }

  startPasswordDetection() {
    console.log('Password Manager: Starting password detection');
    
    // 检测表单提交事件
    this.detectPasswordForms();
    
    // 添加全局按钮点击监听器
    this.addGlobalButtonListener();
    
    // 添加全局回车键监听器
    this.addGlobalEnterKeyListener();
    
    // 自动填充已保存的密码
    this.autoFillPasswords();
    
    // 监听页面变化，处理动态加载的表单
    this.observePageChanges();
  }

  // 检测密码输入框和用户名输入框
  detectPasswordForms() {
    const forms = document.querySelectorAll('form');
    console.log('Password Manager: Detecting forms, found:', forms.length);
    
    forms.forEach((form, index) => {
      if (form.dataset.passwordDetected) return;
      form.dataset.passwordDetected = 'true';
      
      console.log(`Password Manager: Processing form ${index + 1}:`, form.className, form.action);
      
      // 监听表单提交（主要检测方式）
      form.addEventListener('submit', (e) => {
        console.log('>>>1')
        this.handleFormSubmit(form);
      });
      
      // 监听提交按钮的点击事件（备用检测，主要处理非标准提交）
      const submitButtons = form.querySelectorAll('button[onclick*="Submit"], button[onclick*="submit"], button[onclick*="login"]');
      submitButtons.forEach(button => {
        if (button.dataset.passwordButtonListenerAdded) return;
        button.dataset.passwordButtonListenerAdded = 'true';
        
        button.addEventListener('click', () => {
          // 只处理非type="submit"的按钮，避免与表单submit事件重复
          if (button.type !== 'submit') {
            setTimeout(() => {
              console.log('>>>2')
              this.handleFormSubmit(form);
            }, 100);
          }
        });
      });
      
      // 监听密码字段的输入变化
      const passwordFields = this.findPasswordFields(form);
      passwordFields.forEach(field => {
        if (field.dataset.passwordListenerAdded) return;
        field.dataset.passwordListenerAdded = 'true';
        
        // 当密码字段失去焦点时检测
        field.addEventListener('blur', () => {
          this.handlePasswordFieldBlur(form);
        });
        
        // 当用户按Enter键时检测
        field.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            // 先暂存密码数据
            this.handlePasswordFieldBlur(form);
            
            // 延迟检查是否触发了提交，如果是则显示密码保存提示
            setTimeout(() => {
              this.handleEnterKeySubmit(form);
            }, 100);
          }
        });
      });
    });
    
    // 也检测没有form包装的独立密码字段
    this.detectStandalonePasswordFields();
  }

  // 处理表单提交
  handleFormSubmit(form) {
    console.log('Password Manager: Form submit detected');
    
    // 防止重复处理 - 使用节流机制
    const now = Date.now();
    const formId = form.action || form.id || 'default';
    const throttleKey = `submit_${formId}`;
    
    if (this.lastSubmitTime && this.lastSubmitTime[throttleKey] && 
        now - this.lastSubmitTime[throttleKey] < 1000) {
      console.log('Password Manager: Throttling duplicate submit');
      return;
    }
    
    if (!this.lastSubmitTime) this.lastSubmitTime = {};
    this.lastSubmitTime[throttleKey] = now;
    
    // 优先使用暂存的密码数据
    let domain, username, password;
    
    if (this.tempPasswordData) {
      ({ domain, username, password } = this.tempPasswordData);
      console.log('Password Manager: Using cached password data');
    } else {
      const passwordFields = this.findPasswordFields(form);
      const usernameFields = this.findUsernameFields(form);
      
      console.log('Password Manager: Found password fields:', passwordFields.length);
      console.log('Password Manager: Found username fields:', usernameFields.length);
      
      if (passwordFields.length === 0) {
        console.log('Password Manager: No password fields found, aborting');
        return;
      }
      
      username = this.getFieldValue(usernameFields);
      password = this.getFieldValue(passwordFields);
      domain = this.extractDomain(window.location.href);
      
      console.log('Password Manager: Extracted values:', { domain, username: username ? 'found' : 'not found', password: password ? 'found' : 'not found' });
    }
    
    if (username && password) {
      console.log('Password Manager: Triggering password save prompt');
      // 立即显示提示框（不延迟）
      this.checkAndPromptPasswordSave(domain, username, password);
      
      // 清除暂存数据
      this.tempPasswordData = null;
    } else {
      console.log('Password Manager: Missing username or password, not showing prompt');
    }
  }

  // 处理密码字段失去焦点
  handlePasswordFieldBlur(form) {
    const passwordFields = this.findPasswordFields(form);
    const usernameFields = this.findUsernameFields(form);
    
    if (passwordFields.length === 0) return;
    
    const username = this.getFieldValue(usernameFields);
    const password = this.getFieldValue(passwordFields);
    
    // 暂存密码信息，但不立即显示提示（等待表单提交时显示）
    if (username && password && username.length > 0 && password.length > 2) {
      const domain = this.extractDomain(window.location.href);
      this.tempPasswordData = { domain, username, password };
    }
  }

  // 添加全局按钮点击监听器
  addGlobalButtonListener() {
    document.addEventListener('click', (e) => {
      // 检查是否是提交按钮
      if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT') {
        const isSubmitButton = e.target.type === 'submit' || 
                              e.target.getAttribute('onclick')?.includes('Submit') ||
                              e.target.getAttribute('onclick')?.includes('submit') ||
                              e.target.getAttribute('onclick')?.includes('login') ||
                              e.target.textContent?.includes('登录') ||
                              e.target.textContent?.includes('Login');
        
        if (isSubmitButton) {
          console.log('Password Manager: Submit button clicked globally:', e.target);
          
          // 查找最近的表单
          const form = e.target.closest('form') || document.querySelector('form');
          if (form) {
            setTimeout(() => {
              console.log('>>>3')
              this.handleFormSubmit(form);
            }, 200); // 增加延迟确保其他脚本执行完毕
          }
        }
      }
    }, true); // 使用捕获阶段确保我们能先处理
  }

  // 添加全局回车键监听器
  addGlobalEnterKeyListener() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        // 检查焦点是否在输入字段上
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
          // 检查是否是密码字段或者在包含密码字段的容器中
          const isPasswordField = activeElement.type === 'password';
          const form = activeElement.closest('form');
          
          if (isPasswordField || (form && this.findPasswordFields(form).length > 0) || 
              (!form && this.findPasswordFields().length > 0)) {
            
            // 延迟检查，给其他脚本时间处理回车键事件
            setTimeout(() => {
              this.handleGlobalEnterKeySubmit(activeElement);
            }, 150);
          }
        }
      }
    }, true);
  }

  // 检测独立的密码字段（不在form中的）
  detectStandalonePasswordFields() {
    const passwordFields = this.findPasswordFields();
    
    passwordFields.forEach(field => {
      // 跳过已经在form中的字段
      if (field.closest('form')) return;
      if (field.dataset.passwordListenerAdded) return;
      
      field.dataset.passwordListenerAdded = 'true';
      
      // 当密码字段失去焦点时检测
      field.addEventListener('blur', () => {
        this.handleStandalonePasswordField(field);
      });
      
      // 当用户按Enter键时检测
      field.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          // 先暂存密码数据
          this.handleStandalonePasswordField(field);
          
          // 延迟检查是否触发了提交，如果是则显示密码保存提示
          setTimeout(() => {
            this.handleEnterKeySubmitStandalone();
          }, 100);
        }
      });
    });
  }

  // 处理独立的密码字段
  handleStandalonePasswordField(passwordField) {
    const usernameFields = this.findUsernameFields();
    
    const username = this.getFieldValue(usernameFields);
    const password = passwordField.value;
    
    if (username && password && username.length > 0 && password.length > 2) {
      const domain = this.extractDomain(window.location.href);
      
      // 只暂存密码信息，不直接触发提示
      // 等待用户点击提交按钮时由全局按钮监听器处理
      this.tempPasswordData = { domain, username, password };
    }
  }

  // 处理回车键提交检测（form内的密码字段）
  handleEnterKeySubmit(form) {
    // 如果有暂存的密码数据，说明用户刚刚按了回车键
    if (this.tempPasswordData) {
      const { domain, username, password } = this.tempPasswordData;
      console.log('Password Manager: Enter key submit detected in form');
      
      // 立即显示密码保存提示
      this.checkAndPromptPasswordSave(domain, username, password);
      
      // 清除暂存数据
      this.tempPasswordData = null;
    }
  }

  // 处理回车键提交检测（独立密码字段）
  handleEnterKeySubmitStandalone() {
    // 如果有暂存的密码数据，说明用户刚刚按了回车键
    if (this.tempPasswordData) {
      const { domain, username, password } = this.tempPasswordData;
      console.log('Password Manager: Enter key submit detected in standalone field');
      
      // 立即显示密码保存提示
      this.checkAndPromptPasswordSave(domain, username, password);
      
      // 清除暂存数据
      this.tempPasswordData = null;
    }
  }

  // 处理全局回车键提交检测
  handleGlobalEnterKeySubmit(activeElement) {
    const form = activeElement.closest('form');
    
    if (form) {
      // 如果在表单内，尝试从表单提取密码信息
      const passwordFields = this.findPasswordFields(form);
      const usernameFields = this.findUsernameFields(form);
      
      if (passwordFields.length > 0) {
        const username = this.getFieldValue(usernameFields);
        const password = this.getFieldValue(passwordFields);
        
        if (username && password && username.length > 0 && password.length > 2) {
          const domain = this.extractDomain(window.location.href);
          console.log('Password Manager: Global enter key submit detected in form');
          this.checkAndPromptPasswordSave(domain, username, password);
        }
      }
    } else {
      // 如果不在表单内，检查独立字段
      const passwordFields = this.findPasswordFields();
      const usernameFields = this.findUsernameFields();
      
      if (passwordFields.length > 0) {
        const username = this.getFieldValue(usernameFields);
        const password = this.getFieldValue(passwordFields);
        
        if (username && password && username.length > 0 && password.length > 2) {
          const domain = this.extractDomain(window.location.href);
          console.log('Password Manager: Global enter key submit detected in standalone fields');
          this.checkAndPromptPasswordSave(domain, username, password);
        }
      }
    }
  }

  // 查找密码字段
  findPasswordFields(container = document) {
    const selectors = [
      'input[type="password"]',
      'input[name*="password"]',
      'input[name*="passwd"]',  // 匹配这个特定案例
      'input[name="passwd"]',   // 精确匹配
      'input[id*="password"]',
      'input[id*="passwd"]'
    ];
    
    const fields = [];
    selectors.forEach(selector => {
      fields.push(...Array.from(container.querySelectorAll(selector)));
    });
    
    // 去重
    return [...new Set(fields)];
  }

  // 查找用户名字段（支持通配匹配）
  findUsernameFields(container = document) {
    const selectors = [
      'input[type="text"][name*="user"]',
      'input[type="text"][name*="login"]',
      'input[type="text"][name*="email"]',
      'input[type="email"]',
      'input[type="text"][id*="user"]',
      'input[type="text"][id*="login"]',
      'input[type="text"][id*="email"]',
      'input[type="text"][placeholder*="用户"]',
      'input[type="text"][placeholder*="邮箱"]',
      'input[type="text"][placeholder*="手机"]',
      'input[type="text"][placeholder*="账号"]',
      'input[type="text"][placeholder*="username"]',
      'input[type="text"][placeholder*="email"]',
      'input[type="text"][placeholder*="login"]',
      'input[type="text"][class*="user"]',
      'input[type="text"][class*="login"]',
      'input[type="text"][class*="email"]',
      // 添加更多常见的用户名字段模式
      'input[name*="account"]',
      'input[id*="account"]',
      'input[name*="username"]',
      'input[id*="username"]',
      'input[name*="loginname"]',  // 匹配这个特定案例
      'input[id*="loginname"]',
      'input[name*="mobile"]',
      'input[id*="mobile"]',
      'input[name*="phone"]',
      'input[id*="phone"]',
      'input[name="loginname"]',  // 精确匹配
      'input[placeholder*="Username"]',  // 匹配英文占位符
      'input[placeholder*="username"]'
    ];
    
    const fields = [];
    selectors.forEach(selector => {
      fields.push(...Array.from(container.querySelectorAll(selector)));
    });
    
    // 如果没有找到明显的用户名字段，尝试通过位置关系查找
    if (fields.length === 0) {
      const passwordFields = this.findPasswordFields(container);
      if (passwordFields.length > 0) {
        // 查找密码字段前面的文本输入框
        const firstPasswordField = passwordFields[0];
        const textInputs = Array.from(container.querySelectorAll('input[type="text"], input[type="email"], input:not([type])'));
        
        // 找到密码字段前面最近的文本输入框
        for (let i = 0; i < textInputs.length; i++) {
          const textInput = textInputs[i];
          if (this.isFieldBefore(textInput, firstPasswordField)) {
            fields.push(textInput);
          }
        }
      }
    }
    
    // 去重
    return [...new Set(fields)];
  }

  // 判断一个字段是否在另一个字段之前（在DOM中的位置或视觉位置）
  isFieldBefore(field1, field2) {
    // 检查DOM位置
    const position = field1.compareDocumentPosition(field2);
    if (position & Node.DOCUMENT_POSITION_FOLLOWING) {
      return true;
    }
    
    // 检查视觉位置（y坐标）
    try {
      const rect1 = field1.getBoundingClientRect();
      const rect2 = field2.getBoundingClientRect();
      
      // 如果field1在field2上方或同一行但在左边
      return rect1.top < rect2.top || (Math.abs(rect1.top - rect2.top) < 10 && rect1.left < rect2.left);
    } catch {
      return false;
    }
  }

  // 获取字段值
  getFieldValue(fields) {
    for (const field of fields) {
      if (field.value && field.value.trim()) {
        return field.value.trim();
      }
    }
    return null;
  }

  // 提取域名
  extractDomain(url) {
    try {
      const domain = new URL(url).hostname;
      // 移除www前缀
      return domain.replace(/^www\./, '');
    } catch {
      return url;
    }
  }

  // 检查并提示保存密码
  async checkAndPromptPasswordSave(domain, username, password) {
    try {
      // 避免重复提示同一个密码
      const promptKey = `${domain}:${username}:${password}`;
      if (this.pendingPasswordData === promptKey) {
        return;
      }
      
      // 检查是否已保存此密码
      const existingPasswords = await ipcRenderer.invoke('get-passwords', domain);
      const existingPassword = existingPasswords.find(p => p.username === username);
      
      console.log('Password Manager: Checking existing password for', username);
      if (existingPassword) {
        console.log('Password Manager: Found existing password, checking if same');
      }
      
      if (existingPassword && existingPassword.password === password) {
        // 密码已存在且相同，不需要提示
        return;
      }
      
      // 记录当前正在处理的密码，避免重复提示
      this.pendingPasswordData = promptKey;
      
      // 通过IPC发送到主进程显示提示框，这样页面跳转时不会消失
      ipcRenderer.sendToHost('show-password-save-prompt', {
        domain,
        username,
        password,
        isUpdate: !!existingPassword
      });
    } catch (error) {
      console.error('Error checking password:', error);
    }
  }

  // 自动填充密码
  async autoFillPasswords() {
    try {
      const domain = this.extractDomain(window.location.href);
      const passwords = await ipcRenderer.invoke('get-passwords', domain);
      
      if (passwords.length === 0) return;
      
      // 等待一会儿让页面完全加载
      setTimeout(() => {
        this.fillPasswordFields(passwords);
      }, 1000);
    } catch (error) {
      console.error('Error auto-filling passwords:', error);
    }
  }

  // 填充密码字段
  fillPasswordFields(passwords) {
    const passwordFields = this.findPasswordFields();
    const usernameFields = this.findUsernameFields();
    
    if (passwordFields.length === 0 || passwords.length === 0) return;
    
    // 优先使用第一个匹配的密码
    const password = passwords[0];
    
    // 填充用户名
    if (usernameFields.length > 0) {
      const usernameField = usernameFields[0];
      usernameField.value = password.username;
      usernameField.dispatchEvent(new Event('input', { bubbles: true }));
      usernameField.dispatchEvent(new Event('change', { bubbles: true }));
    }
    
    // 填充密码
    if (passwordFields.length > 0) {
      const passwordField = passwordFields[0];
      passwordField.value = password.password;
      passwordField.dispatchEvent(new Event('input', { bubbles: true }));
      passwordField.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // 监听页面变化
  observePageChanges() {
    const observer = new MutationObserver((mutations) => {
      let shouldRecheck = false;
      
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === 1) {
            // 检查是否包含表单元素
            if (node.tagName === 'FORM' || node.querySelector('form') || 
                node.querySelector('input[type="password"]')) {
              shouldRecheck = true;
            }
          }
        });
      });
      
      if (shouldRecheck) {
        setTimeout(() => {
          this.detectPasswordForms();
          this.autoFillPasswords();
        }, 500);
      }
    });
    
    if (document.body) {
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }
  }
}

// 初始化密码管理器
const passwordManager = new PasswordManager(); 